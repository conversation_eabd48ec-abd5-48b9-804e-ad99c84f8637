import os
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QPixmap, QIcon
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
                             QTableWidgetItem, QPushButton, QLabel, QHeaderView)
from .utils import get_song_cover


class CoverLoadThread(QThread):
    """封面加载线程"""
    cover_loaded = pyqtSignal(int, QPixmap)
    
    def __init__(self, row, file_path):
        super().__init__()
        self.row = row
        self.file_path = file_path
    
    def run(self):
        cover = get_song_cover(self.file_path)
        if cover:
            # 调整封面大小以占满表格单元格（不保持宽高比，完全填充）
            cover = cover.scaled(120, 120, Qt.IgnoreAspectRatio, Qt.SmoothTransformation)
            self.cover_loaded.emit(self.row, cover)


class SongTableWidget(QWidget):
    """歌曲表格组件"""
    
    def __init__(self, music_manager, config):
        super().__init__()
        self.music_manager = music_manager
        self.config = config
        self.current_page = 0
        self.total_pages = 0
        self.songs_per_page = config.get('entry_per_page', 100)
        self.cover_threads = []
        
        self.init_ui()
        self.load_songs()
    
    def init_ui(self):
        layout = QVBoxLayout()
        
        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(5)
        self.table.setHorizontalHeaderLabels(['封面', '歌手', '歌曲', '导入时间', '文件大小'])
        
        # 设置表格属性
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.verticalHeader().setVisible(False)
        
        # 设置列宽 - 支持手动调整
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # 封面列固定宽度
        header.setSectionResizeMode(1, QHeaderView.Interactive)  # 歌手列可调整
        header.setSectionResizeMode(2, QHeaderView.Interactive)  # 歌曲列可调整
        header.setSectionResizeMode(3, QHeaderView.Interactive)  # 时间列可调整
        header.setSectionResizeMode(4, QHeaderView.Interactive)  # 大小列可调整
        
        self.table.setColumnWidth(0, 140)  # 封面列宽度，进一步增大

        # 设置初始列宽，让表格占满整个宽度
        self.set_initial_column_widths()

        layout.addWidget(self.table)
        
        # 分页控件
        page_layout = QHBoxLayout()
        page_layout.setContentsMargins(0, 2, 0, 2)  # 进一步减少边距

        self.prev_button = QPushButton('上一页')
        self.prev_button.clicked.connect(self.prev_page)
        self.prev_button.setMaximumWidth(80)  # 限制按钮宽度
        page_layout.addWidget(self.prev_button)

        self.page_label = QLabel('第 1 页，共 1 页')
        self.page_label.setAlignment(Qt.AlignCenter)
        page_layout.addWidget(self.page_label)

        self.next_button = QPushButton('下一页')
        self.next_button.clicked.connect(self.next_page)
        self.next_button.setMaximumWidth(80)  # 限制按钮宽度
        page_layout.addWidget(self.next_button)

        layout.addLayout(page_layout)
        self.setLayout(layout)

        # 设置定时器用于监听窗口大小变化
        self.resize_timer = QTimer()
        self.resize_timer.setSingleShot(True)
        self.resize_timer.timeout.connect(self.adjust_column_widths)

    def resizeEvent(self, event):
        """窗口大小变化事件"""
        super().resizeEvent(event)
        # 延迟调整列宽，避免频繁调整
        self.resize_timer.stop()
        self.resize_timer.start(100)

    def set_initial_column_widths(self):
        """设置初始列宽，让表格占满整个宽度"""
        # 使用QTimer延迟执行，确保表格已经完全渲染
        QTimer.singleShot(100, self._set_column_widths_delayed)

    def adjust_column_widths(self):
        """调整列宽以适应当前窗口大小"""
        self._set_column_widths_delayed()

    def _set_column_widths_delayed(self):
        """延迟设置列宽"""
        # 获取表格实际宽度
        table_width = self.table.width()
        if table_width < 100:  # 如果还没有正确的宽度，再次延迟
            QTimer.singleShot(100, self._set_column_widths_delayed)
            return

        cover_width = 140  # 增大封面列宽度
        # 更精确地计算可用宽度，减去表格边框和内边距
        frame_width = self.table.frameWidth() * 2  # 表格边框宽度
        # 检查是否有垂直滚动条
        scrollbar_width = 0
        if self.table.verticalScrollBar().isVisible():
            scrollbar_width = self.table.verticalScrollBar().sizeHint().width()

        # 计算剩余宽度，减去封面列、边框和滚动条
        remaining_width = table_width - cover_width - frame_width - scrollbar_width - 2

        # 确保剩余宽度为正数
        if remaining_width < 100:
            remaining_width = 100

        # 按比例分配剩余宽度
        singer_width = int(remaining_width * 0.25)  # 25%
        song_width = int(remaining_width * 0.35)    # 35%
        time_width = int(remaining_width * 0.25)    # 25%
        size_width = remaining_width - singer_width - song_width - time_width  # 剩余

        self.table.setColumnWidth(1, singer_width)
        self.table.setColumnWidth(2, song_width)
        self.table.setColumnWidth(3, time_width)
        self.table.setColumnWidth(4, size_width)

    def load_songs(self):
        """加载歌曲数据"""
        try:
            # 获取总数
            total_count = self.music_manager.DB.get_songs_count()
            self.total_pages = (total_count + self.songs_per_page - 1) // self.songs_per_page
            
            # 获取当前页数据
            offset = self.current_page * self.songs_per_page
            songs = self.music_manager.DB.get_all_songs(self.songs_per_page, offset)
            
            # 清空表格
            self.table.setRowCount(0)
            
            # 停止所有封面加载线程
            for thread in self.cover_threads:
                thread.quit()
                thread.wait()
            self.cover_threads.clear()
            
            # 填充表格
            for i, song in enumerate(songs):
                self.table.insertRow(i)
                
                # 封面列（使用QLabel来显示封面，确保完全填充）
                cover_label = QLabel()
                cover_label.setAlignment(Qt.AlignCenter)
                cover_label.setStyleSheet("QLabel { margin: 2px; }")
                cover_label.setScaledContents(True)  # 让图片缩放填充整个标签
                self.table.setCellWidget(i, 0, cover_label)
                
                # 歌手列
                singer_item = QTableWidgetItem(song.singer)
                singer_item.setFlags(singer_item.flags() & ~Qt.ItemIsEditable)
                singer_item.setTextAlignment(Qt.AlignCenter)  # 居中对齐
                self.table.setItem(i, 1, singer_item)

                # 歌曲列
                song_item = QTableWidgetItem(song.song)
                song_item.setFlags(song_item.flags() & ~Qt.ItemIsEditable)
                song_item.setTextAlignment(Qt.AlignCenter)  # 居中对齐
                self.table.setItem(i, 2, song_item)

                # 导入时间列
                time_item = QTableWidgetItem(song.import_time)
                time_item.setFlags(time_item.flags() & ~Qt.ItemIsEditable)
                time_item.setTextAlignment(Qt.AlignCenter)  # 居中对齐
                self.table.setItem(i, 3, time_item)

                # 文件大小列
                file_path = self.music_manager.MusicLibrarian.get_song_file_path(
                    f"{song.singer} - {song.song}.mp3"
                )
                size_text = self.get_file_size_text(file_path)
                size_item = QTableWidgetItem(size_text)
                size_item.setFlags(size_item.flags() & ~Qt.ItemIsEditable)
                size_item.setTextAlignment(Qt.AlignCenter)  # 居中对齐
                self.table.setItem(i, 4, size_item)
                
                # 异步加载封面
                if os.path.exists(file_path):
                    thread = CoverLoadThread(i, file_path)
                    thread.cover_loaded.connect(self.on_cover_loaded)
                    thread.start()
                    self.cover_threads.append(thread)
            
            # 设置行高
            for i in range(self.table.rowCount()):
                self.table.setRowHeight(i, 130)  # 增大行高以适应更大的封面
            
            self.update_page_controls()
            
        except Exception as e:
            print(f"加载歌曲数据时出错: {e}")
    
    def on_cover_loaded(self, row, pixmap):
        """封面加载完成回调"""
        if row < self.table.rowCount():
            cover_label = self.table.cellWidget(row, 0)
            if cover_label and isinstance(cover_label, QLabel):
                cover_label.setPixmap(pixmap)
    
    def get_file_size_text(self, file_path):
        """获取文件大小文本"""
        try:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                if size < 1024:
                    return f"{size} B"
                elif size < 1024 * 1024:
                    return f"{size / 1024:.1f} KB"
                else:
                    return f"{size / (1024 * 1024):.1f} MB"
            return "未知"
        except:
            return "未知"
    
    def update_page_controls(self):
        """更新分页控件状态"""
        self.page_label.setText(f'第 {self.current_page + 1} 页，共 {max(1, self.total_pages)} 页')
        self.prev_button.setEnabled(self.current_page > 0)
        self.next_button.setEnabled(self.current_page < self.total_pages - 1)
    
    def prev_page(self):
        """上一页"""
        if self.current_page > 0:
            self.current_page -= 1
            self.load_songs()
    
    def next_page(self):
        """下一页"""
        if self.current_page < self.total_pages - 1:
            self.current_page += 1
            self.load_songs()
    
    def refresh(self):
        """刷新数据"""
        self.load_songs()
    
    def update_config(self, config):
        """更新配置"""
        self.config = config
        old_per_page = self.songs_per_page
        self.songs_per_page = config.get('entry_per_page', 100)
        
        # 如果每页条目数改变，重新计算当前页
        if old_per_page != self.songs_per_page:
            current_item = self.current_page * old_per_page
            self.current_page = current_item // self.songs_per_page
            self.load_songs()
